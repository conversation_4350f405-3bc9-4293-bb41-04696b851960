const SonyLoginInterceptor = require('./sony-login-interceptor');

async function runExample() {
    console.log('🚀 Sony Login Request Interceptor Example');
    console.log('=========================================');
    
    // Create interceptor instance
    const interceptor = new SonyLoginInterceptor();
    
    // Set your desired credentials that will replace the original ones
    interceptor.setCredentials(
        '<EMAIL>',  // Replace with your target email
        'your-replacement-password'            // Replace with your target password
    );
    
    try {
        // Start the interceptor
        await interceptor.start();
        
        console.log('\n📋 Instructions:');
        console.log('1. The browser window should now be open with the Sony login page');
        console.log('2. Fill in any email/password in the login form');
        console.log('3. When you submit the form, the interceptor will:');
        console.log('   - Catch the POST request to ca.account.sony.com/api/v1/ssocookie');
        console.log('   - Replace the email and password with your configured values');
        console.log('   - Update the Content-Length header');
        console.log('   - Forward the modified request');
        console.log('4. Check the console for interception logs');
        console.log('\n⚠️  Note: Make sure to update the credentials in this script before running!');
        
        // Wait for user interaction
        await interceptor.waitForInteraction(15); // Wait 15 minutes
        
    } catch (error) {
        console.error('❌ Error running example:', error);
    } finally {
        await interceptor.stop();
    }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n🛑 Shutting down...');
    process.exit(0);
});

// Run the example
if (require.main === module) {
    runExample();
}

module.exports = runExample;
