const RequestInterceptor = require('./request-interceptor');
const config = require('./config');

class SonyLoginInterceptor {
    constructor(customConfig = {}) {
        // Merge custom config with default config
        this.config = { ...config, ...customConfig };
        this.interceptor = new RequestInterceptor(this.config);
    }

    /**
     * Set custom credentials
     */
    setCredentials(email, password) {
        this.config.credentials.email = email;
        this.config.credentials.password = password;
        
        // Update the transformations with new credentials
        this.updateTransformations();
    }

    /**
     * Update transformation functions with current credentials
     */
    updateTransformations() {
        const rule = this.config.interceptRules[0];
        if (rule && rule.transformations) {
            // Update email transformation
            rule.transformations[0].replacement = (match, p1, p2, p3, p4, p5) => {
                if (p1 && p2) return p1 + this.config.credentials.email + p2;
                if (p3 && p4) return p3 + this.config.credentials.email + p4;
                if (p5 !== undefined) return 'email=' + encodeURIComponent(this.config.credentials.email);
                return match;
            };

            // Update password transformation
            rule.transformations[1].replacement = (match, p1, p2, p3) => {
                if (p1 && p2) return p1 + this.config.credentials.password + p2;
                if (p3 !== undefined) return 'password=' + encodeURIComponent(this.config.credentials.password);
                return match;
            };
        }
    }

    /**
     * Start the interception process
     */
    async start() {
        try {
            console.log('🎯 Starting Sony Login Interceptor...');
            console.log(`📧 Target Email: ${this.config.credentials.email}`);
            console.log(`🔒 Target Password: ${'*'.repeat(this.config.credentials.password.length)}`);
            
            // Initialize the interceptor
            await this.interceptor.init();
            
            // Navigate to Sony login page
            console.log('🌐 Navigating to Sony login page...');
            await this.interceptor.goto(this.config.targetUrl);
            
            console.log('✅ Page loaded. The interceptor is now active.');
            console.log('📡 Waiting for login requests to intercept...');
            console.log('💡 You can now interact with the login form in the browser.');
            console.log('🔍 When you submit the form, the POST data will be automatically modified.');
            
            return this.interceptor;
            
        } catch (error) {
            console.error('❌ Error starting interceptor:', error);
            throw error;
        }
    }

    /**
     * Wait for user interaction and keep the browser open
     */
    async waitForInteraction(timeoutMinutes = 10) {
        console.log(`⏳ Keeping browser open for ${timeoutMinutes} minutes...`);
        console.log('💡 Press Ctrl+C to stop the interceptor');
        
        return new Promise((resolve) => {
            setTimeout(() => {
                console.log('⏰ Timeout reached. Closing browser...');
                resolve();
            }, timeoutMinutes * 60 * 1000);
        });
    }

    /**
     * Stop the interceptor and close browser
     */
    async stop() {
        if (this.interceptor) {
            await this.interceptor.close();
        }
        console.log('🛑 Sony Login Interceptor stopped.');
    }

    /**
     * Get the page instance for manual interaction
     */
    getPage() {
        return this.interceptor ? this.interceptor.getPage() : null;
    }
}

// Main execution function
async function main() {
    const interceptor = new SonyLoginInterceptor();
    
    // You can set custom credentials here
    // interceptor.setCredentials('<EMAIL>', 'your-password');
    
    try {
        await interceptor.start();
        
        // Keep the browser open for interaction
        await interceptor.waitForInteraction(10); // Wait for 10 minutes
        
    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        await interceptor.stop();
    }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n🛑 Received interrupt signal. Shutting down gracefully...');
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\n🛑 Received terminate signal. Shutting down gracefully...');
    process.exit(0);
});

// Export for use as module
module.exports = SonyLoginInterceptor;

// Run if called directly
if (require.main === module) {
    main();
}
