// Configuration for Sony login request interception
const config = {
    // Browser settings
    headless: false, // Set to true to run in background
    viewport: { width: 1920, height: 1080 },
    timeout: 30000,
    logRequests: true,

    // Target URL to navigate to
    targetUrl: 'https://my.account.sony.com/sonyacct/signin/?duid=0000000700090100473d428780bfc216085221e26ae05c8c49625cf91e2646d50584bfea19de109c&response_type=code&client_id=e4a62faf-4b87-4fea-8565-caaabb3ac918&scope=web%3Acore&access_type=offline&state=c8d5d822d90aa7e97792fa27ed1584f0549e5ce933f03af0c84522a2bc055349&service_entity=urn%3Aservice-entity%3Apsn&ui=pr&smcid=my-playstation&redirect_uri=https%3A%2F%2Fweb.np.playstation.com%2Fapi%2Fsession%2Fv1%2Fsession%3Fredirect_uri%3Dhttps%253A%252F%252Flibrary.playstation.com%252Frecently-purchased%26x-psn-app-ver%3D%2540sie-ppr-web-session%252Fsession%252Fv5.40.5&auth_ver=v3&error=login_required&error_code=4165&error_description=User+is+not+authenticated&no_captcha=true&cid=27dc7241-58e8-4fb4-a128-2b5e26c47290#/signin/input/id',

    // Credentials to replace in the POST data
    credentials: {
        email: '<EMAIL>',
        password: 'your-new-password'
    },

    // Request interception rules
    interceptRules: [
        {
            // Target the Sony SSO cookie API
            urlPattern: 'https://ca\\.account\\.sony\\.com/api/v1/ssocookie',
            method: 'POST',
            logData: true, // Log original and modified data for debugging
            
            // Regex transformations to apply to POST data
            transformations: [
                {
                    // Replace email field - matches various formats like:
                    // "email":"<EMAIL>" or email=<EMAIL> or "user_name":"<EMAIL>"
                    pattern: '("email"\\s*:\\s*")[^"]*(")|("user_name"\\s*:\\s*")[^"]*(")|email=([^&]*)',
                    replacement: (match, p1, p2, p3, p4, p5) => {
                        if (p1 && p2) return p1 + config.credentials.email + p2;
                        if (p3 && p4) return p3 + config.credentials.email + p4;
                        if (p5 !== undefined) return 'email=' + encodeURIComponent(config.credentials.email);
                        return match;
                    },
                    flags: 'gi'
                },
                {
                    // Replace password field - matches various formats like:
                    // "password":"oldpass" or password=oldpass
                    pattern: '("password"\\s*:\\s*")[^"]*(")|password=([^&]*)',
                    replacement: (match, p1, p2, p3) => {
                        if (p1 && p2) return p1 + config.credentials.password + p2;
                        if (p3 !== undefined) return 'password=' + encodeURIComponent(config.credentials.password);
                        return match;
                    },
                    flags: 'gi'
                }
            ]
        }
    ]
};

module.exports = config;
