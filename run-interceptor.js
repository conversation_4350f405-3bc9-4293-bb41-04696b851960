const SonyLoginInterceptor = require('./sony-login-interceptor');

async function main() {
    console.log('🚀 Sony Login Request Interceptor');
    console.log('=================================');
    
    // Create interceptor instance
    const interceptor = new SonyLoginInterceptor();
    
    // ⚠️ IMPORTANT: Set your desired credentials here
    // These will replace whatever username/password is submitted in the form
    interceptor.setCredentials(
        '<EMAIL>',  // Replace with your target username
        'your-target-password'               // Replace with your target password
    );
    
    try {
        console.log('\n📋 How this works:');
        console.log('1. <PERSON><PERSON><PERSON> will open to Sony login page');
        console.log('2. Fill in ANY username/password in the form');
        console.log('3. When you submit, the interceptor will:');
        console.log('   ✅ Catch POST to ca.account.sony.com/api/v1/ssocookie');
        console.log('   ✅ Replace username and password with your configured values');
        console.log('   ✅ Update Content-Length header automatically');
        console.log('   ✅ Forward the modified request');
        console.log('\n🔍 Expected payload format:');
        console.log('{"authentication_type":"password","username":"OLD_USER","password":"OLD_PASS",...}');
        console.log('\n⚡ Starting interceptor...\n');
        
        // Start the interceptor
        await interceptor.start();
        
        // Keep browser open for interaction
        console.log('\n⏳ Browser is ready. Submit the login form to see interception in action.');
        console.log('💡 Press Ctrl+C to stop when done.\n');
        
        // Wait indefinitely until user stops
        await new Promise(() => {}); // Keep running until Ctrl+C
        
    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        await interceptor.stop();
    }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n\n🛑 Stopping interceptor...');
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\n\n🛑 Terminating interceptor...');
    process.exit(0);
});

// Run the interceptor
main().catch(console.error);
