# Sony Login Request Interceptor

A Puppeteer-based script that intercepts and modifies POST requests to Sony's login API, specifically targeting the `https://ca.account.sony.com/api/v1/ssocookie` endpoint.

## What it does

1. **Opens Sony login page** in a browser window
2. **Intercepts POST requests** to the SSO cookie endpoint
3. **Modifies the payload** to replace username and password with your configured values
4. **Updates Content-Length** header automatically
5. **Forwards the modified request** seamlessly

## Payload Structure

The script handles this JSON payload format:
```json
{
  "authentication_type": "password",
  "username": "<EMAIL>",
  "password": "originalpassword",
  "dfp_data": [...],
  "duid": "...",
  "client_id": "..."
}
```

## Setup

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Configure your target credentials:**
   Edit `run-interceptor.js` and update these lines:
   ```javascript
   interceptor.setCredentials(
       '<EMAIL>',  // Your target username
       'your-replacement-password'            // Your target password
   );
   ```

## Usage

### Quick Start
```bash
npm start
```

### Manual Run
```bash
node run-interceptor.js
```

## How to Use

1. **Run the script** - Browser window opens to Sony login page
2. **Fill in ANY credentials** in the login form (they will be replaced)
3. **Submit the form** - The interceptor will:
   - Catch the POST request
   - Replace username/password with your configured values
   - Update Content-Length header
   - Forward the modified request
4. **Check console** for interception logs
5. **Press Ctrl+C** to stop when done

## Files

- `request-interceptor.js` - Core Puppeteer request interception class
- `config.js` - Configuration for URL patterns and transformations
- `sony-login-interceptor.js` - Sony-specific interceptor implementation
- `run-interceptor.js` - Main script to run the interceptor

## Configuration

You can customize the behavior by modifying `config.js`:

- **Target URL** - The Sony login page URL
- **Intercept Rules** - URL patterns and transformation rules
- **Browser Settings** - Headless mode, viewport, timeout, etc.

## Example Output

```
🚀 Sony Login Request Interceptor
=================================
📧 Updated username: <EMAIL>
🔒 Updated password: ******************
🎯 Starting Sony Login Interceptor...
🚀 Launching browser...
✅ Browser initialized successfully
🌐 Navigating to Sony login page...
✅ Page loaded. The interceptor is now active.
📡 Waiting for login requests to intercept...

[When form is submitted:]
📡 POST https://ca.account.sony.com/api/v1/ssocookie
🎯 Intercepting request: https://ca.account.sony.com/api/v1/ssocookie
🔄 POST data transformed:
   Original length: 245
   Modified length: 251
✅ Request modified and forwarded
```

## Security Note

⚠️ **Important**: This tool is for educational and testing purposes. Make sure you have proper authorization before intercepting and modifying requests.

## Troubleshooting

- **Browser doesn't open**: Check if Puppeteer installed correctly
- **No interception**: Verify the URL pattern matches the actual request
- **Credentials not replaced**: Check the JSON payload format matches expected structure
- **Content-Length errors**: The script automatically updates this header
