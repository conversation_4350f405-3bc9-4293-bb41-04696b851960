const SonyLoginInterceptor = require('./sony-login-interceptor');

async function testInterceptor() {
    console.log('🧪 Testing Sony Login Interceptor');
    console.log('================================');
    
    const interceptor = new SonyLoginInterceptor();
    
    // Set test credentials that will replace whatever is submitted
    interceptor.setCredentials(
        '<EMAIL>',
        'test-replacement-password'
    );
    
    console.log('\n📋 Test Configuration:');
    console.log('✅ Target URL: Sony login page with your specific parameters');
    console.log('✅ Intercept URL: https://ca.account.sony.com/api/v1/ssocookie');
    console.log('✅ Method: POST');
    console.log('✅ Expected payload format: JSON with username/password fields');
    console.log('\n🚀 Starting test...\n');
    
    try {
        await interceptor.start();
        
        console.log('✅ Browser opened successfully!');
        console.log('✅ Request interception is active!');
        console.log('\n📝 Next steps:');
        console.log('1. Fill in ANY email/password in the login form');
        console.log('2. Submit the form');
        console.log('3. Watch the console for interception logs');
        console.log('4. The POST data will be modified automatically');
        console.log('\n💡 Expected behavior:');
        console.log('   Original: {"username":"<EMAIL>","password":"whatever"}');
        console.log('   Modified: {"username":"<EMAIL>","password":"test-replacement-password"}');
        console.log('\n⏳ Browser will stay open for 5 minutes for testing...');
        
        // Wait for 5 minutes to allow testing
        await new Promise(resolve => setTimeout(resolve, 5 * 60 * 1000));
        
        console.log('\n⏰ Test timeout reached. Closing browser...');
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    } finally {
        await interceptor.stop();
        console.log('🏁 Test completed.');
    }
}

// Handle Ctrl+C to stop test early
process.on('SIGINT', () => {
    console.log('\n🛑 Test stopped by user.');
    process.exit(0);
});

// Run the test
testInterceptor().catch(console.error);
